// Copyright iYuuki Studio


#include "AbilitySystem/ExecCalc/ExecCalc_Damage.h"

#include "AbilitySystemComponent.h"
#include "AuraAbilityTypes.h"
#include "AuraGameplayTags.h"
#include "AbilitySystem/AuraAttributeSet.h"
#include "Interaction/CombatInterface.h"
#include "System/AuraAbilitySystemLibrary.h"

struct AuraDamageStatics
{
	DECLARE_ATTRIBUTE_CAPTUREDEF(Armor);
	DECLARE_ATTRIBUTE_CAPTUREDEF(ArmorPenetration);
	DECLARE_ATTRIBUTE_CAPTUREDEF(BlockChance);
	DECLARE_ATTRIBUTE_CAPTUREDEF(CriticalHitChance);
	DECLARE_ATTRIBUTE_CAPTUREDEF(CriticalHitDamage);
	DECLARE_ATTRIBUTE_CAPTUREDEF(CriticalResistance);

	DECLARE_ATTRIBUTE_CAPTUREDEF(ResistanceFire);
	DECLARE_ATTRIBUTE_CAPTUREDEF(ResistanceLightning);
	DECLARE_ATTRIBUTE_CAPTUREDEF(ResistanceArcane);
	DECLARE_ATTRIBUTE_CAPTUREDEF(ResistancePhysical);

	TMap<FGameplayTag, FGameplayEffectAttributeCaptureDefinition> TagToDefs;

	AuraDamageStatics()
	{
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, Armor, Target, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, ArmorPenetration, Source, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, BlockChance, Target, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, CriticalHitChance, Source, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, CriticalHitDamage, Source, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, CriticalResistance, Target, false);

		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, ResistanceFire, Target, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, ResistanceLightning, Target, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, ResistanceArcane, Target, false);
		DEFINE_ATTRIBUTE_CAPTUREDEF(UAuraAttributeSet, ResistancePhysical, Target, false);

		const FAuraGameplayTags& GameplayTags = FAuraGameplayTags::Get();
		TagToDefs.Add(GameplayTags.Attributes_Secondary_Armor, ArmorDef);
		TagToDefs.Add(GameplayTags.Attributes_Secondary_ArmorPenetration, ArmorPenetrationDef);
		TagToDefs.Add(GameplayTags.Attributes_Secondary_BlockChance, BlockChanceDef);
		TagToDefs.Add(GameplayTags.Attributes_Secondary_CriticalHitChance, CriticalHitChanceDef);
		TagToDefs.Add(GameplayTags.Attributes_Secondary_CriticalHitDamage, CriticalHitDamageDef);
		TagToDefs.Add(GameplayTags.Attributes_Secondary_CriticalResistance, CriticalResistanceDef);

		TagToDefs.Add(GameplayTags.Attributes_Resistance_Fire, ResistanceFireDef);
		TagToDefs.Add(GameplayTags.Attributes_Resistance_Lightning, ResistanceLightningDef);
		TagToDefs.Add(GameplayTags.Attributes_Resistance_Arcane, ResistanceArcaneDef);
		TagToDefs.Add(GameplayTags.Attributes_Resistance_Physical, ResistancePhysicalDef);
	}
};

static const AuraDamageStatics& DamageStatics()
{
	static AuraDamageStatics Instance;
	return Instance;
}

UExecCalc_Damage::UExecCalc_Damage()
{
	RelevantAttributesToCapture.Add(DamageStatics().ArmorDef);
	RelevantAttributesToCapture.Add(DamageStatics().BlockChanceDef);
	RelevantAttributesToCapture.Add(DamageStatics().ArmorPenetrationDef);
	RelevantAttributesToCapture.Add(DamageStatics().CriticalHitChanceDef);
	RelevantAttributesToCapture.Add(DamageStatics().CriticalHitDamageDef);
	RelevantAttributesToCapture.Add(DamageStatics().CriticalResistanceDef);
	RelevantAttributesToCapture.Add(DamageStatics().ResistanceFireDef);
	RelevantAttributesToCapture.Add(DamageStatics().ResistanceLightningDef);
	RelevantAttributesToCapture.Add(DamageStatics().ResistanceArcaneDef);
	RelevantAttributesToCapture.Add(DamageStatics().ResistancePhysicalDef);
}

void UExecCalc_Damage::Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams,
                                              FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
	const FGameplayEffectSpec& EffectSpec = ExecutionParams.GetOwningSpec();
	const UAbilitySystemComponent* SourceASC = ExecutionParams.GetSourceAbilitySystemComponent();
	const UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();

	AActor* SourceAvatarActor = SourceASC ? SourceASC->GetAvatarActor() : nullptr;
	AActor* TargetAvatarActor = TargetASC ? TargetASC->GetAvatarActor() : nullptr;
	ICombatInterface* SourceAvatarCombatInterface = Cast<ICombatInterface>(SourceAvatarActor);
	ICombatInterface* TargetAvatarCombatInterface = Cast<ICombatInterface>(TargetAvatarActor);

	const FGameplayTagContainer* SourceTags = EffectSpec.CapturedSourceTags.GetAggregatedTags();
	const FGameplayTagContainer* TargetTags = EffectSpec.CapturedTargetTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluateParameters;
	EvaluateParameters.SourceTags = SourceTags;
	EvaluateParameters.TargetTags = TargetTags;

	// Get Damage
	float Damage = 0.f;
	for (const auto& Pair : FAuraGameplayTags::Get().DamageTypesToResistances)
	{

		const FGameplayTag DamageTypeTag = Pair.Key;
		const FGameplayTag ResistanceTag = Pair.Value;
		
		const float Value = EffectSpec.GetSetByCallerMagnitude(DamageTypeTag);

		const FGameplayEffectAttributeCaptureDefinition& CaptureDef = DamageStatics().TagToDefs[ResistanceTag];
		float Resistance = 0.f;
		ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(CaptureDef, EvaluateParameters, Resistance);

		const float ElementalDamage = Value * (100 - Resistance) / 100;

		Damage += ElementalDamage;
	}


	// Get Armor
	float Armor = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().ArmorDef, EvaluateParameters, Armor);
	Armor = FMath::Max<float>(Armor, 0.f);

	// Get BlockChange
	float BlockChance = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().BlockChanceDef, EvaluateParameters, BlockChance);

	// Get ArmorPenetration
	float ArmorPenetration = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().ArmorPenetrationDef, EvaluateParameters, ArmorPenetration);

	// Get CriticalHitChance
	float CriticalHitChance = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().CriticalHitChanceDef, EvaluateParameters, CriticalHitChance);

	// Get CriticalHitDamage
	float CriticalHitDamage = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().CriticalHitDamageDef, EvaluateParameters, CriticalHitDamage);

	// Get CriticalResistance
	float CriticalResistance = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().CriticalResistanceDef, EvaluateParameters, CriticalResistance);

	// Get DamageCalculationData
	const UCurveTable* DamageCalculation = UAuraAbilitySystemLibrary::GetDamageCalculationData(TargetAvatarActor);
	FRealCurve* ArmorCurve = DamageCalculation->FindCurve(FName("Armor"), TEXT("Armor Curve"));
	FRealCurve* ArmorPenetrationCurve = DamageCalculation->FindCurve(FName("ArmorPenetration"), TEXT("Block Curve"));

	// Get Enemy Level
	float EnemyLevel = TargetAvatarCombatInterface->GetCharacterLevel();

	// Start Calculating Damage
	Armor = Armor * ArmorCurve->Eval(EnemyLevel, 1);
	ArmorPenetration = ArmorPenetration * ArmorPenetrationCurve->Eval(EnemyLevel, 1);
	float AfterArmorPenetration = Armor - ArmorPenetration;

	float CalculatedDamage = Damage;

	bool bCriticalHit = FMath::RandRange(1, 100) <= CriticalHitChance;
	if (bCriticalHit)
	{
		CalculatedDamage = CalculatedDamage * CriticalHitDamage - CriticalResistance;
	}

	CalculatedDamage = CalculatedDamage - AfterArmorPenetration;

	if (CalculatedDamage < Damage) CalculatedDamage = Damage;

	bool bBlocked = FMath::RandRange(1, 100) <= BlockChance;
	if (bBlocked)
	{
		CalculatedDamage *= 0.5f;
	}

	FGameplayEffectContextHandle Context = EffectSpec.GetContext();

	UAuraAbilitySystemLibrary::SetIsBlockedHit(Context, bBlocked);
	UAuraAbilitySystemLibrary::SetIsCriticalHit(Context, bCriticalHit);

	FGameplayModifierEvaluatedData EvaluatedData(UAuraAttributeSet::GetIncomingDamageAttribute(),
	                                             EGameplayModOp::Additive, CalculatedDamage);
	OutExecutionOutput.AddOutputModifier(EvaluatedData);
}
