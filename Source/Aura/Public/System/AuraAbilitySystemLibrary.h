// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystem/Data/AuraCharacterClassInfo.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "AuraAbilitySystemLibrary.generated.h"

struct FGameplayEffectContextHandle;
class UGameplayAbility;
class UAbilitySystemComponent;
class UEnemyWidgetController;
class AAuraHUD;
class UAttributeMenuWidgetController;
class UOverlayWidgetController;
class UAuraWidgetController;
struct FWidgetControllerParams;
/**
 * 
 */
UCLASS()
class AURA_API UAuraAbilitySystemLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|WidgetController")
	static UOverlayWidgetController* GetOverlayWidgetController(const UObject* WorldContextObject);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|WidgetController")
	static UAttributeMenuWidgetController* GetAttributeMenuWidgetController(const UObject* WorldContextObject);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|DefaultInfo")
	static UAuraCharacterClassInfo* GetCharacterClassInfo(const UObject* WorldContextObject);

	UFUNCTION(Category = "AuraAbilitySystemLibrary|DefaultInfo")
static void InitializeDefaultAttributes(const UObject* WorldContextObject,
                                        const ECharacterClass& CharacterClass, float Level,
                                        UAbilitySystemComponent* ASC);

	UFUNCTION(Category = "AuraAbilitySystemLibrary|DefaultInfo")
	static void GiveStartupAbilities(const UObject* WorldContextObject,
	                                 UAbilitySystemComponent* ASC);

	UFUNCTION(Category = "AuraAbilitySystemLibrary|DefaultInfo")
	static UCurveTable* GetDamageCalculationData(const UObject* WorldContextObject);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static bool IsBlockedHit(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintPure, Category="AuraAbilitySystemLibrary|Combat")
	static bool IsCriticalHit(const FGameplayEffectContextHandle& EffectContextHandle);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetIsBlockedHit(UPARAM(ref) FGameplayEffectContextHandle& EffectContextHandle, bool bInIsBlockedHit);

	UFUNCTION(BlueprintCallable, Category="AuraAbilitySystemLibrary|Combat")
	static void SetIsCriticalHit(UPARAM(ref) FGameplayEffectContextHandle& EffectContextHandle, bool bInIsCriticalHit);

private:
	static FWidgetControllerParams GetOverlayWidgetControllerParams(const UObject* WorldContextObject,
	                                                                AAuraHUD*& OutAuraHUD);
};
