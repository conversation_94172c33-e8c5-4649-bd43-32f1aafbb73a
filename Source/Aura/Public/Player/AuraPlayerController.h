// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "AuraPlayerController.generated.h"

class UDamageTextComponent;
class UAuraAbilitySystemComponent;
struct FGameplayTag;
class UAuraInputConfig;
class UInputMappingContext;
class UInputAction;
struct FInputActionValue;
class IEnemyInterface;
class USplineComponent;
/**
 * 
 */
UCLASS()
class AURA_API AAuraPlayerController : public APlayerController
{
	GENERATED_BODY()

public:
	AAuraPlayerController();

	UPROPERTY()
	FHitResult CursorHitResult;

	UFUNCTION(Client, Reliable)
	void ShowDamageFloatText(const float Value, AActor* TargetActor, bool bBlockedHit = false, bool bCriticalHit = false);

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaSeconds) override;
	virtual void SetupInputComponent() override;

private:
	void Move(const FInputActionValue& InputActionValue);
	void ShiftPressed(const FInputActionValue& InputActionValue) { bShiftPressed = true; };
	void ShiftReleased(const FInputActionValue& InputActionValue) { bShiftPressed = false; };
	void TraceCursor();
	void AutoRun();

	void AbilityInputTagPressed(FGameplayTag InputTag);
	void AbilityInputTagHeld(FGameplayTag InputTag);
	void AbilityInputTagReleased(FGameplayTag InputTag);

	UAuraAbilitySystemComponent* GetAuraASC();

	UPROPERTY(EditAnywhere, Category = Input)
	TObjectPtr<UInputMappingContext> AuraContext;
	UPROPERTY(EditAnywhere, Category = Input)
	TObjectPtr<UInputAction> MoveAction;
	UPROPERTY(EditAnywhere, Category = Input)
	TObjectPtr<UInputAction> ShiftAction;
	UPROPERTY(EditAnywhere, Category = Input)
	TObjectPtr<UInputAction> LookAction;

	UPROPERTY(EditAnywhere, Category = Input)
	TObjectPtr<UAuraInputConfig> InputConfig;

	UPROPERTY()
	TObjectPtr<UAuraAbilitySystemComponent> AuraASC;

	TScriptInterface<IEnemyInterface> LastCursorTraceActor;
	TScriptInterface<IEnemyInterface> CurrentCursorTraceActor;

	bool bTargeting = false;
	FVector CachedDestination;
	float FollowTime;
	float ShortPressThreshold = 0.5f;
	bool bAutoRunning = false;

	bool bShiftPressed = false;

	UPROPERTY(EditAnywhere, Category = Input)
	float AutoRunAcceptanceRadius = 50.f;

	UPROPERTY(VisibleAnywhere)
	TObjectPtr<USplineComponent> SplineComponent;

	UPROPERTY(EditAnywhere, Category = "Damage Text")
	TSubclassOf<UDamageTextComponent> DamageTextComponentClass;
};
